// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/deliveries/data/models/delivery.dart';
import 'package:sphere/features/project/data/models/parameters.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';

export 'contract_status.dart';
export 'provision_search_filters.dart';

part 'index.freezed.dart';
part 'index.g.dart';

@freezed
class GetRequirementsOutput with _$GetRequirementsOutput {
  @JsonSerializable(includeIfNull: false)
  factory GetRequirementsOutput({
    List<ProvisionProductModel>? items,
    int? totalItems,
  }) = _GetRequirementsOutput;

  factory GetRequirementsOutput.fromJson(Map<String, dynamic> json) =>
      _$GetRequirementsOutputFromJson(json);
}

@freezed
class ProvisionsCreateModel with _$ProvisionsCreateModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProvisionsCreateModel({
    String? provisionName,
    ProvisionsFilter? filterType,
    List<ProvisionsCreateItemModel>? items,
    List<DeliveryGroupModel>? deliveryGroups,
  }) = _ProvisionsCreateModel;

  factory ProvisionsCreateModel.fromJson(Map<String, dynamic> json) =>
      _$ProvisionsCreateModelFromJson(json);
}

@freezed
class ProvisionsCreateItemModel with _$ProvisionsCreateItemModel {
  @JsonSerializable(includeIfNull: false)
  const factory ProvisionsCreateItemModel({
    String? productId,
    ParametersFeatureType? featureType,
    double? quantity,
  }) = _ProvisionsCreateItemModel;

  factory ProvisionsCreateItemModel.fromJson(Map<String, dynamic> json) =>
      _$ProvisionsCreateItemModelFromJson(json);
}

@freezed
class ProvisionsGetMaterials with _$ProvisionsGetMaterials {
  @JsonSerializable(includeIfNull: false)
  const factory ProvisionsGetMaterials({
    ProvisionsFilter? filter,
  }) = _ProvisionsGetMaterials;

  factory ProvisionsGetMaterials.fromJson(Map<String, dynamic> json) =>
      _$ProvisionsGetMaterialsFromJson(json);
}

@freezed
class ProvisionsColumnOptionsInput with _$ProvisionsColumnOptionsInput {
  @JsonSerializable(includeIfNull: false)
  const factory ProvisionsColumnOptionsInput({
    String? projectId,
    ProvisionColumn? column,
    String? search,
    ProvisionsFilter? filterType,
  }) = _ProvisionsColumnOptionsInput;

  factory ProvisionsColumnOptionsInput.fromJson(Map<String, dynamic> json) =>
      _$ProvisionsColumnOptionsInputFromJson(json);
}

@freezed
class ProvisionsColumnOptionsOutput with _$ProvisionsColumnOptionsOutput {
  @JsonSerializable(includeIfNull: false)
  const factory ProvisionsColumnOptionsOutput({
    List<String>? items,
    int? totalItems,
  }) = _ProvisionsColumnOptionsOutput;

  factory ProvisionsColumnOptionsOutput.fromJson(Map<String, dynamic> json) =>
      _$ProvisionsColumnOptionsOutputFromJson(json);
}

@freezed
class ProvisionCreateLotInput with _$ProvisionCreateLotInput {
  @JsonSerializable(includeIfNull: false)
  factory ProvisionCreateLotInput({
    String? projectId,
    String? lotName,
    List<String>? productIds,
    String? plannedTenderCompletionDate,
  }) = _ProvisionCreateLotInput;

  factory ProvisionCreateLotInput.fromJson(Map<String, dynamic> json) =>
      _$ProvisionCreateLotInputFromJson(json);
}

@freezed
class ProvisionAddToLotInput with _$ProvisionAddToLotInput {
  @JsonSerializable(includeIfNull: false)
  factory ProvisionAddToLotInput({
    // String? projectId,
    List<int>? lotNumbers,
    List<String>? productIds,
  }) = _ProvisionAddToLotInput;

  factory ProvisionAddToLotInput.fromJson(Map<String, dynamic> json) =>
      _$ProvisionAddToLotInputFromJson(json);
}

@freezed
class ProvisionCreateContractInput with _$ProvisionCreateContractInput {
  @JsonSerializable(includeIfNull: false)
  factory ProvisionCreateContractInput({
    required List<String> productIds, // ID продуктов
    // required String contractNumber, // Номер договора
    @JsonKey(name: 'contractDate')
    required String contractDate, // Дата заключения
    @JsonKey(name: 'contractStartDate')
    String? contractStartDate, // Дата начала
    @JsonKey(name: 'contractEndDate') String? contractEndDate, // Дата окончания
    String? supplierId, // ID поставщика
    @JsonKey(name: 'plannedDeliveryDate')
    String? plannedDeliveryDate, // Плановая дата поставки
    required double contractPrice, // Общая сумма контракта
    @JsonKey(name: 'productDetails')
    List<ProductDetail>? productDetails, // Детали продуктов
    @JsonKey(name: 'paymentDetails')
    List<PaymentDetail>? paymentDetails, // Детали оплат
    ProvisionsFilter? filterType,
    String? notes, // Примечания
  }) = _ProvisionCreateContractInput;

  factory ProvisionCreateContractInput.fromJson(Map<String, dynamic> json) =>
      _$ProvisionCreateContractInputFromJson(json);
}

@freezed
class ProductDetail with _$ProductDetail {
  @JsonSerializable(includeIfNull: false)
  factory ProductDetail({
    String? productId, // ID продукта
    String? productName,
    double? price, // Цена (>= 0)
    UnitType? unit, // Единица измерения
    List<Distribution>? distributions, // Распределения по складам/датам
    String? notes, // Примечания
  }) = _ProductDetail;

  factory ProductDetail.fromJson(Map<String, dynamic> json) =>
      _$ProductDetailFromJson(json);
}

@freezed
class Distribution with _$Distribution {
  @JsonSerializable(includeIfNull: false)
  factory Distribution({
    String? warehouseId, // ID склада
    double? quantity, // Количество (>= 0)
    String? deliveryDate, // Дата поставки (YYYY-MM-DD)
    String? notes, // Примечания для распределения
  }) = _Distribution;

  factory Distribution.fromJson(Map<String, dynamic> json) =>
      _$DistributionFromJson(json);
}

@freezed
class PaymentDetail with _$PaymentDetail {
  @JsonSerializable(includeIfNull: false)
  factory PaymentDetail({
    @JsonKey(name: 'paymentDate') String? paymentDate, // Дата оплаты
    double? amount, // Сумма оплаты
  }) = _PaymentDetail;

  factory PaymentDetail.fromJson(Map<String, dynamic> json) =>
      _$PaymentDetailFromJson(json);
}

@freezed
class ProvisionUpdateContractInput with _$ProvisionUpdateContractInput {
  @JsonSerializable(includeIfNull: false)
  factory ProvisionUpdateContractInput({
    required String contractId, // ID контракта для обновления
    required List<String> productIds, // ID продуктов
    @JsonKey(name: 'contractDate')
    required String contractDate, // Дата заключения
    @JsonKey(name: 'contractStartDate')
    String? contractStartDate, // Дата начала
    @JsonKey(name: 'contractEndDate') String? contractEndDate, // Дата окончания
    String? supplierId, // ID поставщика
    @JsonKey(name: 'plannedDeliveryDate')
    String? plannedDeliveryDate, // Плановая дата поставки
    required double contractPrice, // Общая сумма контракта
    @JsonKey(name: 'productDetails')
    List<ProductDetail>? productDetails, // Детали продуктов
    @JsonKey(name: 'paymentDetails')
    List<PaymentDetail>? paymentDetails, // Детали оплат
    ProvisionsFilter? filterType,
    String? notes, // Примечания
  }) = _ProvisionUpdateContractInput;

  factory ProvisionUpdateContractInput.fromJson(Map<String, dynamic> json) =>
      _$ProvisionUpdateContractInputFromJson(json);
}

@freezed
class ProvisionChangeContractStatusInput
    with _$ProvisionChangeContractStatusInput {
  @JsonSerializable(includeIfNull: false)
  factory ProvisionChangeContractStatusInput({
    String? contractId,
    ContractAction? action,
    String? reason,
  }) = _ProvisionChangeContractStatusInput;

  factory ProvisionChangeContractStatusInput.fromJson(
          Map<String, dynamic> json) =>
      _$ProvisionChangeContractStatusInputFromJson(json);
}

@freezed
class SearchWarehousesInput with _$SearchWarehousesInput {
  @JsonSerializable(includeIfNull: false)
  factory SearchWarehousesInput({
    String? projectId,
    bool? includeContractors,
    WarehouseType? type,
  }) = _SearchWarehousesInput;

  factory SearchWarehousesInput.fromJson(Map<String, dynamic> json) =>
      _$SearchWarehousesInputFromJson(json);
}

@freezed
class SearchWarehousesOutput with _$SearchWarehousesOutput {
  @JsonSerializable(includeIfNull: false)
  factory SearchWarehousesOutput({
    List<Warehouse>? warehouses,
    int? total,
  }) = _SearchWarehousesOutput;

  factory SearchWarehousesOutput.fromJson(Map<String, dynamic> json) =>
      _$SearchWarehousesOutputFromJson(json);
}

enum ContractAction {
  confirm,
  revert,
  cancel;

  String get displayName {
    switch (this) {
      case ContractAction.confirm:
        return 'Подтвердить';
      case ContractAction.revert:
        return 'Отменить';
      case ContractAction.cancel:
        return 'Отменить';
    }
  }
}

enum ProvisionColumn {
  drawingNumber,
  name,
  parentName,
  material,
  feature,
  requirements,
  materialRequirements,
  priority,
  contractNumber,
  supplier,
  lotNumber,
  lotName,
}

enum ProvisionsFilter {
  cooperation,
  materials,
  @JsonValue('assembly_materials')
  assemblyMaterials,
  purchased,
  assembly;

  String getName() {
    switch (this) {
      case ProvisionsFilter.cooperation:
        return 'Кооперация';
      case ProvisionsFilter.materials:
        return 'Материалы';
      case ProvisionsFilter.assemblyMaterials:
        return 'Материалы СБ';
      case ProvisionsFilter.purchased:
        return 'Покупные';
      case ProvisionsFilter.assembly:
        return 'СБ';
    }
  }

  String getBackendValue() {
    switch (this) {
      case ProvisionsFilter.cooperation:
        return 'cooperation';
      case ProvisionsFilter.materials:
        return 'materials';
      case ProvisionsFilter.assemblyMaterials:
        return 'assembly_materials';
      case ProvisionsFilter.purchased:
        return 'purchased';
      case ProvisionsFilter.assembly:
        return 'assembly';
    }
  }
}

// Модели для складов
@freezed
class Warehouse with _$Warehouse {
  @JsonSerializable(includeIfNull: false)
  factory Warehouse({
    @JsonKey(name: '_id') String? id, // ID склада
    required String name, // Название склада
    @JsonKey(name: 'type') required WarehouseType type, // Тип склада
    String? address, // Адрес склада (для физических складов)
    String? description, // Описание склада
    String? projectId, // ID проекта (для виртуальных складов проекта)
    String?
        physicalWarehouseId, // ID физического склада (для виртуальных складов)
    String? contractorId, // ID контрагента (для складов контрагента)
    List<WarehouseKeeper>? keepers, // Кладовщики склада
    bool? isActive, // Активен ли склад
    @JsonKey(name: 'createdAt') String? createdAt, // Дата создания
    @JsonKey(name: 'updatedAt') String? updatedAt, // Дата обновления
    String? createdBy, // ID пользователя, создавшего склад
    String? updatedBy, // ID пользователя, обновившего склад
  }) = _Warehouse;

  factory Warehouse.fromJson(Map<String, dynamic> json) =>
      _$WarehouseFromJson(json);
}

@freezed
class WarehouseKeeper with _$WarehouseKeeper {
  @JsonSerializable(includeIfNull: false)
  factory WarehouseKeeper({
    String? id, // ID кладовщика
    String? name, // Имя кладовщика
    String? email, // Email кладовщика
    bool? isActive, // Активен ли кладовщик
  }) = _WarehouseKeeper;

  factory WarehouseKeeper.fromJson(Map<String, dynamic> json) =>
      _$WarehouseKeeperFromJson(json);
}

enum WarehouseType {
  @JsonValue('general')
  general,
  @JsonValue('contractor')
  contractor,
  @JsonValue('virtual_project')
  virtualProject,
}

@freezed
class WarehouseCreateInput with _$WarehouseCreateInput {
  @JsonSerializable(includeIfNull: false)
  factory WarehouseCreateInput({
    required String name, // Название склада
    @JsonKey(name: 'type') required WarehouseType type, // Тип склада
    String? address, // Адрес склада
    String? description, // Описание склада
  }) = _WarehouseCreateInput;

  factory WarehouseCreateInput.fromJson(Map<String, dynamic> json) =>
      _$WarehouseCreateInputFromJson(json);
}

@freezed
class WarehouseUpdateInput with _$WarehouseUpdateInput {
  @JsonSerializable(includeIfNull: false)
  factory WarehouseUpdateInput({
    required String warehouseId, // ID склада
    String? name, // Название склада
    String? address, // Адрес склада
    String? description, // Описание склада
  }) = _WarehouseUpdateInput;

  factory WarehouseUpdateInput.fromJson(Map<String, dynamic> json) =>
      _$WarehouseUpdateInputFromJson(json);
}

@freezed
class WarehouseDeleteInput with _$WarehouseDeleteInput {
  @JsonSerializable(includeIfNull: false)
  factory WarehouseDeleteInput({
    required String warehouseId, // ID склада
  }) = _WarehouseDeleteInput;

  factory WarehouseDeleteInput.fromJson(Map<String, dynamic> json) =>
      _$WarehouseDeleteInputFromJson(json);
}

@freezed
class WarehouseViewInput with _$WarehouseViewInput {
  @JsonSerializable(includeIfNull: false)
  factory WarehouseViewInput({
    required String warehouseId, // ID склада
  }) = _WarehouseViewInput;

  factory WarehouseViewInput.fromJson(Map<String, dynamic> json) =>
      _$WarehouseViewInputFromJson(json);
}

@freezed
class WarehouseSearchInput with _$WarehouseSearchInput {
  @JsonSerializable(includeIfNull: false)
  factory WarehouseSearchInput({
    String? projectId, // ID проекта для фильтрации складов
    bool?
        includeContractors, // Включать склады контрагентов (по умолчанию true)
    WarehouseType? type, // Тип склада для фильтрации
  }) = _WarehouseSearchInput;

  factory WarehouseSearchInput.fromJson(Map<String, dynamic> json) =>
      _$WarehouseSearchInputFromJson(json);
}

@freezed
class WarehouseSearchOutput with _$WarehouseSearchOutput {
  @JsonSerializable(includeIfNull: false)
  factory WarehouseSearchOutput({
    List<Warehouse>? warehouses, // Список складов
    int? total, // Общее количество складов
  }) = _WarehouseSearchOutput;

  factory WarehouseSearchOutput.fromJson(Map<String, dynamic> json) =>
      _$WarehouseSearchOutputFromJson(json);
}
